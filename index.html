
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>高容量QR码生成器与解码器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lz-string/1.4.4/lz-string.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qr-scanner/1.4.2/qr-scanner.umd.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        textarea {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
        .input-group {
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .input-group label {
            display: inline-block;
            margin: 8px 20px 8px 0;
        }
        select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background-color: #0056b3;
        }
        #qrcode {
            margin: 20px 0;
            text-align: center;
        }
        #status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background-color: #e9ecef;
        }
        .advanced-options {
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }
        .info-text {
            color: #666;
            font-size: 14px;
            margin: 5px 0;
        }
        #fileInputArea {
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 4px;
            text-align: center;
            margin: 10px 0;
        }
        #fileInfo {
            margin-top: 10px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 4px;
        }
        .file-input {
            margin: 10px 0;
        }
        .tab-container {
            margin-bottom: 20px;
        }
        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #ddd;
        }
        .tab-button {
            padding: 10px 20px;
            background-color: #e9ecef;
            border: 1px solid #ced4da;
            border-bottom: 2px solid transparent;
            cursor: pointer;
            font-size: 16px;
            margin-right: 5px;
            color: #495057;
        }
        .tab-button.active {
            background-color: white;
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .tab-button:hover:not(.active) {
            background-color: #f8f9fa;
            color: #007bff;
            border-color: #007bff;
        }
        .tab-content {
            display: none;
            padding: 20px 0;
        }
        .tab-content.active {
            display: block;
        }
        #videoContainer {
            position: relative;
            width: 100%;
            max-width: 500px;
            margin: 20px auto;
        }
        #qrVideo {
            width: 100%;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .decoder-controls {
            text-align: center;
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
        }
        .chunk-status {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin: 10px 0;
        }
        .chunk-indicator {
            width: 20px;
            height: 20px;
            border: 1px solid #ddd;
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            background-color: #f8f9fa;
        }
        .chunk-indicator.received {
            background-color: #28a745;
            color: white;
        }
        #decodedOutput {
            width: 100%;
            height: 200px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
            background-color: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>高容量QR码生成器与解码器</h1>
        <div class="info-text">
            支持大文本传输和文件传输，自动分块和压缩
        </div>

        <div class="tab-container">
            <div class="tab-buttons">
                <button class="tab-button active" onclick="switchTab('encoder')">编码器</button>
                <button class="tab-button" onclick="switchTab('decoder')">解码器</button>
            </div>
        </div>

        <!-- 编码器标签页 -->
        <div id="encoder" class="tab-content active">
            <div class="input-group">
            <div id="fileInputArea">
                <input type="file" id="fileInput" class="file-input">
                <div id="fileInfo"></div>
            </div>
            <textarea id="textInput" placeholder="或者在此输入文本内容..."></textarea>
            <div class="advanced-options">
                <label>
                    <select id="compressionMode">
                        <option value="none">无压缩</option>
                        <option value="lz" selected>LZ压缩（标准）</option>
                        <option value="lz-utf16">LZ-UTF16（高效）</option>
                        <option value="multi">多级压缩（最高效）</option>
                        <option value="adaptive">自适应压缩（智能）</option>
                    </select> 压缩模式
                </label>
                <label>
                    <input type="number" id="chunkSize" value="1500" min="100" max="8000"> 每块数据大小
                </label>
                <label>
                    <input type="checkbox" id="smartMode" checked> 智能传输模式
                </label>
                <label>
                    <select id="refreshRate">
                        <option value="50">0.05秒（超极速）</option>
                        <option value="100">0.1秒（极速）</option>
                        <option value="200">0.2秒（高速）</option>
                        <option value="500" selected>0.5秒（快速）</option>
                        <option value="1000">1.0秒（标准）</option>
                        <option value="2000">2.0秒（稳定）</option>
                    </select> 刷新速度
                </label>
                <label>
                    <select id="errorCorrection">
                        <option value="L">低纠错（最快）</option>
                        <option value="M" selected>中纠错（平衡）</option>
                        <option value="Q">高纠错（稳定）</option>
                        <option value="H">最高纠错（最稳定）</option>
                    </select> 纠错级别
                </label>
                <label>
                    <input type="checkbox" id="blockSelectionMode"> 启用块选择模式
                </label>
                <div id="blockSelectionControls" style="display: none;">
                    <label>
                        <input type="text" id="blockSelection" placeholder="例如: 1,2,9,10,20-30" style="width: 200px;"> 选择块范围
                    </label>
                    <div class="info-text" style="font-size: 12px; color: #666; margin-top: 5px;">
                        支持格式：单个块(1,2,9)、范围(20-30)、组合(1,2,9,10,20-30)
                    </div>
                </div>
            </div>
                <button onclick="generateQR()">生成QR码</button>
                <div id="status"></div>
            </div>
            <div id="qrcode"></div>
        </div>

        <!-- 解码器标签页 -->
        <div id="decoder" class="tab-content">
            <div class="input-group">
                <h3>QR码解码器</h3>
                <div class="info-text">
                    使用摄像头扫描滚动QR码，自动收集和重组数据
                </div>

                <div class="decoder-controls">
                    <button id="startDecoding" onclick="startDecoding()">开始扫描</button>
                    <button id="stopDecoding" onclick="stopDecoding()" disabled>停止扫描</button>
                    <button onclick="resetDecoder()">重置</button>
                </div>

                <div style="text-align: center; margin: 20px 0;">
                    <div style="border-top: 1px solid #ddd; margin: 20px 0;"></div>
                    <div class="info-text">或者上传QR码图片文件</div>
                    <input type="file" id="qrImageInput" accept="image/*" style="margin: 10px;" onchange="handleImageUpload(event)">
                </div>

                <div id="videoContainer">
                    <video id="qrVideo" autoplay muted playsinline></video>
                </div>

                <div id="decodingStatus">
                    <div class="info-text">等待开始扫描...</div>
                    <div class="progress-bar">
                        <div id="progressFill" class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div id="chunkStatus" class="chunk-status"></div>
                </div>

                <div>
                    <label>解码结果:</label>
                    <textarea id="decodedOutput" readonly placeholder="解码后的内容将显示在这里..."></textarea>
                    <button onclick="downloadDecodedData()" id="downloadBtn" disabled>下载文件</button>
                    <button onclick="copyToClipboard()" id="copyBtn" disabled>复制文本</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 高级压缩工具类
        class AdvancedCompressor {
            static compress(data, mode) {
                const originalSize = new Blob([data]).size;
                console.log('原始数据大小:', originalSize, 'bytes');

                let compressed, compressionType;

                switch (mode) {
                    case 'none':
                        compressed = btoa(unescape(encodeURIComponent(data)));
                        compressionType = 0;
                        break;

                    case 'lz':
                        compressed = LZString.compressToBase64(data);
                        compressionType = 1;
                        break;

                    case 'lz-utf16':
                        compressed = LZString.compressToUTF16(data);
                        // 再次Base64编码以确保QR码兼容性
                        compressed = btoa(unescape(encodeURIComponent(compressed)));
                        compressionType = 2;
                        break;

                    case 'multi':
                        // 多级压缩：先LZ压缩，再尝试进一步优化
                        let step1 = LZString.compressToUTF16(data);
                        let step2 = LZString.compressToBase64(step1);
                        compressed = step2;
                        compressionType = 3;
                        break;

                    case 'adaptive':
                        // 自适应：尝试多种方法，选择最优的
                        const methods = [
                            { data: LZString.compressToBase64(data), type: 1 },
                            { data: btoa(unescape(encodeURIComponent(LZString.compressToUTF16(data)))), type: 2 },
                            { data: LZString.compressToBase64(LZString.compressToUTF16(data)), type: 3 }
                        ];

                        // 选择压缩率最高的方法
                        const best = methods.reduce((a, b) => a.data.length < b.data.length ? a : b);
                        compressed = best.data;
                        compressionType = best.type;
                        break;

                    default:
                        compressed = LZString.compressToBase64(data);
                        compressionType = 1;
                }

                const compressedSize = new Blob([compressed]).size;
                const ratio = ((originalSize - compressedSize) / originalSize * 100).toFixed(1);
                console.log(`压缩完成: ${originalSize} -> ${compressedSize} bytes (节省 ${ratio}%)`);

                return { data: compressed, type: compressionType, ratio: ratio };
            }

            static decompress(data, type) {
                switch (type) {
                    case 0: // 无压缩
                        return decodeURIComponent(escape(atob(data)));

                    case 1: // LZ Base64
                        return LZString.decompressFromBase64(data);

                    case 2: // LZ UTF16
                        const utf16Data = decodeURIComponent(escape(atob(data)));
                        return LZString.decompressFromUTF16(utf16Data);

                    case 3: // 多级压缩
                        const step1 = LZString.decompressFromBase64(data);
                        return LZString.decompressFromUTF16(step1);

                    default:
                        return LZString.decompressFromBase64(data);
                }
            }
        }

        class DataEncoder {
            constructor(data, compressionMode = 'lz') {
                this.rawData = data;
                this.compressionMode = compressionMode;
                this.chunks = [];
                this.compressionInfo = null;
            }

            async processData() {
                try {
                    // 使用高级压缩器
                    const compressionResult = AdvancedCompressor.compress(this.rawData, this.compressionMode);
                    const processedData = compressionResult.data;
                    this.compressionInfo = compressionResult;

                    // 获取块大小设置和智能模式
                    let chunkSize = parseInt(document.getElementById('chunkSize').value) || 1500;
                    const smartMode = document.getElementById('smartMode').checked;
                    const errorLevel = document.getElementById('errorCorrection').value;

                    // 智能模式：根据纠错级别优化块大小
                    if (smartMode) {
                        const optimizedSizes = {
                            'L': Math.min(chunkSize * 1.3, 8000),  // 低纠错可以更大
                            'M': chunkSize,                         // 中纠错保持原样
                            'Q': Math.max(chunkSize * 0.8, 800),   // 高纠错适当减小
                            'H': Math.max(chunkSize * 0.6, 600)    // 最高纠错显著减小
                        };
                        chunkSize = Math.floor(optimizedSizes[errorLevel]);
                        console.log(`智能模式：纠错级别${errorLevel}，优化块大小为${chunkSize}`);
                    }
                    
                    // 分块处理
                    this.chunks = [];
                    for (let i = 0; i < processedData.length; i += chunkSize) {
                        const chunk = processedData.slice(i, i + chunkSize);
                        this.chunks.push(chunk);
                    }
                    
                    console.log('总块数:', this.chunks.length);
                    return true;
                } catch (error) {
                    console.error('数据处理错误:', error);
                    return false;
                }
            }

            generateChunk(index) {
                if (index >= this.chunks.length) return null;

                try {
                    const chunkData = {
                        i: index,                           // 当前块索引
                        t: this.chunks.length,              // 总块数
                        c: this.compressionInfo?.type || 0, // 压缩类型
                        d: this.chunks[index],              // 数据内容
                        r: this.compressionInfo?.ratio || 0 // 压缩率（用于统计）
                    };
                    
                    const jsonStr = JSON.stringify(chunkData);
                    console.log('块大小:', jsonStr.length);
                    return jsonStr;
                } catch (error) {
                    console.error('生成数据块错误:', error);
                    return null;
                }
            }
        }

        async function handleFileSelect(event) {
            const file = event.target.files[0];
            if (!file) return;

            const fileInfo = document.getElementById('fileInfo');
            fileInfo.innerHTML = `选中文件: ${file.name} (${formatFileSize(file.size)})`;
            
            // 清空文本输入框
            document.getElementById('textInput').value = '';

            if (file.size > 10 * 1024 * 1024) { // 10MB限制
                alert('文件过大，请选择小于10MB的文件');
                event.target.value = '';
                fileInfo.innerHTML = '';
                return;
            }

            try {
                const reader = new FileReader();
                reader.onload = async function(e) {
                    const fileContent = e.target.result;
                    // 将文件内容和文件信息组合
                    const fileData = {
                        name: file.name,
                        type: file.type,
                        size: file.size,
                        content: fileContent
                    };
                    document.getElementById('textInput').value = JSON.stringify(fileData);
                };
                reader.readAsDataURL(file);
            } catch (error) {
                console.error('文件读取错误:', error);
                alert('文件读取失败');
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function updateStatus(message) {
            const status = document.getElementById('status');
            status.textContent = message;
        }

        // 解析块选择字符串，支持格式：1,2,9,10,20-30
        function parseBlockSelection(selectionString, totalBlocks) {
            if (!selectionString || selectionString.trim() === '') {
                return [];
            }
            
            const selectedBlocks = new Set();
            const parts = selectionString.split(',').map(s => s.trim());
            
            for (const part of parts) {
                if (part.includes('-')) {
                    // 处理范围：20-30
                    const [start, end] = part.split('-').map(s => parseInt(s.trim()));
                    if (isNaN(start) || isNaN(end)) continue;
                    
                    // 确保范围在有效范围内
                    const validStart = Math.max(0, Math.min(start - 1, totalBlocks - 1)); // 转换为0索引
                    const validEnd = Math.max(0, Math.min(end - 1, totalBlocks - 1));
                    
                    for (let i = validStart; i <= validEnd; i++) {
                        selectedBlocks.add(i);
                    }
                } else {
                    // 处理单个块：1,2,9
                    const blockNum = parseInt(part);
                    if (!isNaN(blockNum) && blockNum >= 1 && blockNum <= totalBlocks) {
                        selectedBlocks.add(blockNum - 1); // 转换为0索引
                    }
                }
            }
            
            return Array.from(selectedBlocks).sort((a, b) => a - b);
        }

        async function generateQR() {
            // 停止之前的定时器
            if (currentDisplayTimer) {
                clearTimeout(currentDisplayTimer);
                currentDisplayTimer = null;
            }
            
            const qrcodeDiv = document.getElementById('qrcode');
            qrcodeDiv.innerHTML = '';

            const textInput = document.getElementById('textInput').value;
            if (!textInput) {
                alert('请输入文本或选择文件');
                return;
            }

            const compressionMode = document.getElementById('compressionMode').value;
            const encoder = new DataEncoder(textInput, compressionMode);
            
            updateStatus('正在处理数据...');
            if (!await encoder.processData()) {
                updateStatus('数据处理失败');
                return;
            }

            const totalChunks = encoder.chunks.length;
            let currentChunk = 0;
            
            // 检查是否启用块选择模式
            const blockSelectionMode = document.getElementById('blockSelectionMode').checked;
            let selectedBlocks = [];
            
            if (blockSelectionMode) {
                const blockSelectionString = document.getElementById('blockSelection').value;
                selectedBlocks = parseBlockSelection(blockSelectionString, totalChunks);
                
                if (selectedBlocks.length === 0) {
                    updateStatus('块选择模式：请输入有效的块选择范围');
                    return;
                }
                
                console.log('解析的块选择:', selectedBlocks);
                console.log('总块数:', totalChunks);
                updateStatus(`块选择模式：选择了 ${selectedBlocks.length} 个块 (${selectedBlocks.map(i => i + 1).join(', ')})`);
            } else {
                updateStatus(`总共 ${totalChunks} 个数据块`);
            }

            // 获取纠错级别设置
            const errorCorrectionLevel = document.getElementById('errorCorrection').value;
            const correctionLevels = {
                'L': QRCode.CorrectLevel.L,
                'M': QRCode.CorrectLevel.M,
                'Q': QRCode.CorrectLevel.Q,
                'H': QRCode.CorrectLevel.H
            };

            // 智能模式：根据数据量和纠错级别调整QR码尺寸
            const smartMode = document.getElementById('smartMode').checked;
            let qrSize = 400;

            if (smartMode) {
                // 根据总块数和纠错级别调整尺寸
                const sizeMultipliers = {
                    'L': 0.8,  // 低纠错可以更小
                    'M': 1.0,  // 中纠错标准尺寸
                    'Q': 1.1,  // 高纠错稍大
                    'H': 1.2   // 最高纠错更大
                };

                // 根据块数调整：块数多说明数据密度大，需要更大的QR码
                const blockSizeMultiplier = totalChunks > 10 ? 1.1 :
                                          totalChunks > 5 ? 1.0 : 0.9;

                qrSize = Math.floor(400 * sizeMultipliers[errorCorrectionLevel] * blockSizeMultiplier);
                qrSize = Math.max(300, Math.min(qrSize, 600)); // 限制在300-600之间

                console.log(`智能模式：QR码尺寸调整为${qrSize}px`);
            }

            // 创建QR码实例
            const qrcode = new QRCode(qrcodeDiv, {
                width: qrSize,
                height: qrSize,
                correctLevel: correctionLevels[errorCorrectionLevel]
            });

            // 获取刷新速度
            let refreshRate = parseInt(document.getElementById('refreshRate').value);
            const originalRefreshRate = refreshRate;
            
            // 初始化当前块位置
            let selectedBlockIndex = 0; // 用于跟踪选定块的索引
            
            if (blockSelectionMode && selectedBlocks.length > 0) {
                currentChunk = selectedBlocks[0]; // 从第一个选定的块开始
                selectedBlockIndex = 0;
            }

            function displayNextChunk() {
                // 重新检查块选择模式（防止状态变化）
                const currentBlockSelectionMode = document.getElementById('blockSelectionMode').checked;
                let nextChunk = currentChunk;
                
                console.log('displayNextChunk调用 - 块选择模式:', currentBlockSelectionMode, '选定块数量:', selectedBlocks.length);
                
                if (currentBlockSelectionMode && selectedBlocks.length > 0) {
                    // 块选择模式：在选定的块之间循环
                    nextChunk = selectedBlocks[selectedBlockIndex];
                    console.log('块选择模式：当前索引', selectedBlockIndex, '显示块', nextChunk + 1, '选定块数组:', selectedBlocks.slice(0, 5));
                } else {
                    // 智能模式：根据解码器反馈调整传输策略
                    const smartMode = document.getElementById('smartMode').checked;
                    
                    if (smartMode && decoder && decoder.smartController) {
                        nextChunk = decoder.smartController.getNextChunk(currentChunk, totalChunks);
                        if (decoder.smartController.isRetransmitting()) {
                            // 重传模式：降低速度，提高稳定性
                            refreshRate = Math.max(refreshRate, 200);
                        }
                    } else {
                        nextChunk = (currentChunk + 1) % totalChunks;
                    }
                    console.log('非块选择模式：nextChunk =', nextChunk + 1);
                }

                try {
                    const chunkData = encoder.generateChunk(nextChunk);
                    if (chunkData) {
                        qrcode.clear();
                        qrcode.makeCode(chunkData);

                        let statusText;
                        if (currentBlockSelectionMode && selectedBlocks.length > 0) {
                            const currentPos = selectedBlockIndex + 1;
                            statusText = `块选择模式：显示第 ${nextChunk + 1} 个数据块 (${currentPos}/${selectedBlocks.length}) (${refreshRate}ms刷新)`;
                        } else {
                            const smartMode = document.getElementById('smartMode').checked;
                            const modeText = smartMode && decoder?.smartController?.isRetransmitting() ?
                                ' [重传模式]' : '';
                            statusText = `显示第 ${nextChunk + 1}/${totalChunks} 个数据块 (${refreshRate}ms刷新)${modeText}`;
                        }
                        updateStatus(statusText);

                        currentChunk = nextChunk;
                        
                        // 在块选择模式下，更新索引以指向下一个块
                        if (currentBlockSelectionMode && selectedBlocks.length > 0) {
                            selectedBlockIndex = (selectedBlockIndex + 1) % selectedBlocks.length;
                            console.log('更新索引到:', selectedBlockIndex);
                        }
                    }
                } catch (error) {
                    console.error('生成QR码错误:', error);
                    updateStatus('生成QR码时发生错误');
                    return;
                }

                // 清除之前的定时器，设置新的定时器
                if (currentDisplayTimer) {
                    clearTimeout(currentDisplayTimer);
                }
                currentDisplayTimer = setTimeout(displayNextChunk, refreshRate);
            }

            displayNextChunk();
        }

        // 智能传输控制器
        class SmartTransmissionController {
            constructor() {
                this.missingChunks = new Set();
                this.receivedChunks = new Set();
                this.totalChunks = 0;
                this.retransmissionMode = false;
                this.lastProgressTime = Date.now();
            }

            updateProgress(receivedChunks, totalChunks) {
                this.receivedChunks = new Set(receivedChunks);
                this.totalChunks = totalChunks;
                this.missingChunks.clear();

                for (let i = 0; i < totalChunks; i++) {
                    if (!this.receivedChunks.has(i)) {
                        this.missingChunks.add(i);
                    }
                }

                this.lastProgressTime = Date.now();
            }

            shouldRetransmit() {
                // 如果5秒内没有新进展，启动重传模式
                return Date.now() - this.lastProgressTime > 5000 && this.missingChunks.size > 0;
            }

            getNextChunk(currentChunk, totalChunks) {
                if (this.shouldRetransmit() && this.missingChunks.size > 0) {
                    this.retransmissionMode = true;
                    return Array.from(this.missingChunks)[0];
                }

                this.retransmissionMode = false;
                return (currentChunk + 1) % totalChunks;
            }

            isRetransmitting() {
                return this.retransmissionMode;
            }
        }

        // 解码器类
        class DataDecoder {
            constructor() {
                this.chunks = new Map(); // 存储接收到的数据块
                this.totalChunks = 0;    // 总块数
                this.isCompressed = false; // 是否压缩
                this.isComplete = false;   // 是否完成
                this.lastUpdateTime = Date.now();
                this.smartController = new SmartTransmissionController();
            }

            reset() {
                this.chunks.clear();
                this.totalChunks = 0;
                this.isCompressed = false;
                this.isComplete = false;
                this.lastUpdateTime = Date.now();
                this.smartController = new SmartTransmissionController();
                this.updateUI();
            }

            processChunk(qrData) {
                try {
                    const chunkData = JSON.parse(qrData);

                    // 验证数据格式
                    if (typeof chunkData.i !== 'number' ||
                        typeof chunkData.t !== 'number' ||
                        typeof chunkData.c !== 'number' ||
                        typeof chunkData.d !== 'string') {
                        return false;
                    }

                    // 设置总块数和压缩信息
                    if (this.totalChunks === 0) {
                        this.totalChunks = chunkData.t;
                        this.compressionType = chunkData.c || 0;
                        this.compressionRatio = chunkData.r || 0;
                        this.isCompressed = this.compressionType > 0;
                        this.updateUI();

                        // 显示压缩信息
                        if (this.compressionRatio > 0) {
                            console.log(`检测到压缩数据，压缩率: ${this.compressionRatio}%`);
                        }
                    }

                    // 验证块索引
                    if (chunkData.i < 0 || chunkData.i >= this.totalChunks) {
                        return false;
                    }

                    // 存储数据块
                    if (!this.chunks.has(chunkData.i)) {
                        this.chunks.set(chunkData.i, chunkData.d);
                        this.lastUpdateTime = Date.now();

                        // 更新智能控制器
                        this.smartController.updateProgress(Array.from(this.chunks.keys()), this.totalChunks);

                        this.updateUI();

                        // 检查是否完成
                        if (this.chunks.size === this.totalChunks) {
                            this.assembleData();
                        }
                    }

                    return true;
                } catch (error) {
                    console.error('处理数据块错误:', error);
                    return false;
                }
            }

            assembleData() {
                try {
                    // 按顺序组装数据
                    let assembledData = '';
                    for (let i = 0; i < this.totalChunks; i++) {
                        if (!this.chunks.has(i)) {
                            console.error('缺少数据块:', i);
                            return false;
                        }
                        assembledData += this.chunks.get(i);
                    }

                    // 使用高级解压缩器
                    let finalData;
                    try {
                        finalData = AdvancedCompressor.decompress(assembledData, this.compressionType);
                    } catch (error) {
                        console.error('解压缩失败，尝试兼容模式:', error);
                        // 兼容旧版本
                        if (this.isCompressed) {
                            finalData = LZString.decompressFromBase64(assembledData);
                        } else {
                            finalData = decodeURIComponent(escape(atob(assembledData)));
                        }
                    }

                    if (!finalData) {
                        throw new Error('数据解压缩失败');
                    }

                    this.isComplete = true;
                    this.displayResult(finalData);
                    return true;
                } catch (error) {
                    console.error('组装数据错误:', error);
                    this.updateStatus('数据组装失败: ' + error.message);
                    return false;
                }
            }

            displayResult(data) {
                const output = document.getElementById('decodedOutput');

                try {
                    // 尝试解析为文件数据
                    const fileData = JSON.parse(data);
                    if (fileData.name && fileData.content && fileData.type) {
                        output.value = `文件: ${fileData.name}\n类型: ${fileData.type}\n大小: ${formatFileSize(fileData.size)}\n\n[文件内容已解码，点击下载按钮获取文件]`;
                        this.decodedFileData = fileData;
                        document.getElementById('downloadBtn').disabled = false;
                    } else {
                        throw new Error('不是文件格式');
                    }
                } catch (e) {
                    // 普通文本数据
                    output.value = data;
                    this.decodedFileData = null;
                    document.getElementById('copyBtn').disabled = false;
                }

                this.updateStatus('解码完成！');
            }

            updateUI() {
                const progress = this.totalChunks > 0 ? (this.chunks.size / this.totalChunks) * 100 : 0;
                document.getElementById('progressFill').style.width = progress + '%';

                const statusDiv = document.getElementById('chunkStatus');
                statusDiv.innerHTML = '';

                if (this.totalChunks > 0) {
                    for (let i = 0; i < this.totalChunks; i++) {
                        const indicator = document.createElement('div');
                        indicator.className = 'chunk-indicator';
                        indicator.textContent = i + 1;
                        if (this.chunks.has(i)) {
                            indicator.classList.add('received');
                        }
                        statusDiv.appendChild(indicator);
                    }
                }

                const statusText = this.totalChunks > 0
                    ? `已接收 ${this.chunks.size}/${this.totalChunks} 个数据块 (${progress.toFixed(1)}%)`
                    : '等待扫描QR码...';

                this.updateStatus(statusText);
            }

            updateStatus(message) {
                const statusElement = document.querySelector('#decodingStatus .info-text');
                if (statusElement) {
                    statusElement.textContent = message;
                }
            }
        }

        // 全局变量
        let decoder = new DataDecoder();
        let qrScanner = null;
        let isScanning = false;
        let currentDisplayTimer = null; // 全局定时器变量

        // 标签页切换
        function switchTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有按钮的活动状态
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // 如果切换到解码器，停止当前扫描
            if (tabName === 'decoder' && isScanning) {
                stopDecoding();
            }
        }

        // 检查摄像头支持
        function checkCameraSupport() {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                return {
                    supported: false,
                    message: '您的浏览器不支持摄像头访问功能'
                };
            }

            // 检查是否为安全环境
            const isSecure = location.protocol === 'https:' ||
                           location.hostname === 'localhost' ||
                           location.hostname === '127.0.0.1' ||
                           location.hostname === '';

            if (!isSecure) {
                return {
                    supported: false,
                    message: '摄像头访问需要HTTPS环境或localhost。请使用以下方式之一：\n\n1. 使用 python -m http.server 8000 启动本地服务器，然后访问 http://localhost:8000\n2. 部署到HTTPS网站\n3. 使用文件上传方式代替摄像头扫描'
                };
            }

            return { supported: true };
        }

        // 开始解码
        async function startDecoding() {
            // 检查摄像头支持
            const cameraCheck = checkCameraSupport();
            if (!cameraCheck.supported) {
                alert(cameraCheck.message);
                return;
            }

            try {
                const video = document.getElementById('qrVideo');

                decoder.updateStatus('正在请求摄像头权限...');

                // 请求摄像头权限，先尝试后置摄像头，失败则使用前置
                let stream;
                try {
                    stream = await navigator.mediaDevices.getUserMedia({
                        video: { facingMode: 'environment' } // 优先使用后置摄像头
                    });
                } catch (e) {
                    console.log('后置摄像头不可用，尝试前置摄像头');
                    stream = await navigator.mediaDevices.getUserMedia({
                        video: { facingMode: 'user' } // 使用前置摄像头
                    });
                }

                video.srcObject = stream;

                // 等待视频加载
                await new Promise((resolve) => {
                    video.onloadedmetadata = resolve;
                });

                // 初始化QR扫描器
                if (!qrScanner) {
                    qrScanner = new QrScanner(video, result => {
                        if (decoder.processChunk(result.data)) {
                            console.log('成功处理QR码:', result.data.substring(0, 100) + '...');
                        }
                    }, {
                        returnDetailedScanResult: true,
                        highlightScanRegion: true,
                        highlightCodeOutline: true,
                    });
                }

                await qrScanner.start();
                isScanning = true;

                document.getElementById('startDecoding').disabled = true;
                document.getElementById('stopDecoding').disabled = false;

                decoder.updateStatus('正在扫描QR码...');

            } catch (error) {
                console.error('启动摄像头失败:', error);
                let errorMessage = '无法访问摄像头。';

                if (error.name === 'NotAllowedError') {
                    errorMessage += '\n\n请检查以下设置：\n1. 点击地址栏的摄像头图标，允许摄像头访问\n2. 检查浏览器设置中的摄像头权限\n3. 确保没有其他应用正在使用摄像头';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += '\n\n未检测到摄像头设备，请确保：\n1. 摄像头已正确连接\n2. 摄像头驱动已安装\n3. 尝试刷新页面重试';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += '\n\n您的浏览器不支持摄像头功能，请：\n1. 更新浏览器到最新版本\n2. 尝试使用Chrome、Firefox或Safari\n3. 使用文件上传方式代替';
                } else {
                    errorMessage += '\n\n错误详情：' + error.message;
                }

                alert(errorMessage);
                decoder.updateStatus('摄像头启动失败');
            }
        }

        // 停止解码
        function stopDecoding() {
            if (qrScanner) {
                qrScanner.stop();
            }

            const video = document.getElementById('qrVideo');
            if (video.srcObject) {
                const tracks = video.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                video.srcObject = null;
            }

            isScanning = false;
            document.getElementById('startDecoding').disabled = false;
            document.getElementById('stopDecoding').disabled = true;

            decoder.updateStatus('已停止扫描');
        }

        // 重置解码器
        function resetDecoder() {
            stopDecoding();
            decoder.reset();
            document.getElementById('decodedOutput').value = '';
            document.getElementById('downloadBtn').disabled = true;
            document.getElementById('copyBtn').disabled = true;
        }

        // 下载解码的文件
        function downloadDecodedData() {
            if (!decoder.decodedFileData) {
                alert('没有可下载的文件数据');
                return;
            }

            try {
                const fileData = decoder.decodedFileData;
                const link = document.createElement('a');
                link.href = fileData.content;
                link.download = fileData.name;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            } catch (error) {
                console.error('下载文件失败:', error);
                alert('下载文件失败');
            }
        }

        // 复制文本到剪贴板
        async function copyToClipboard() {
            const output = document.getElementById('decodedOutput');
            if (!output.value) {
                alert('没有可复制的内容');
                return;
            }

            try {
                await navigator.clipboard.writeText(output.value);
                alert('已复制到剪贴板');
            } catch (error) {
                console.error('复制失败:', error);
                // 备用方法
                output.select();
                document.execCommand('copy');
                alert('已复制到剪贴板');
            }
        }

        // 处理图片上传
        async function handleImageUpload(event) {
            const file = event.target.files[0];
            if (!file) return;

            try {
                decoder.updateStatus('正在解析图片中的QR码...');

                // 使用QR-Scanner解析图片
                const result = await QrScanner.scanImage(file, {
                    returnDetailedScanResult: true
                });

                if (decoder.processChunk(result.data)) {
                    decoder.updateStatus('成功解析QR码图片');
                    console.log('从图片解析QR码:', result.data.substring(0, 100) + '...');
                } else {
                    decoder.updateStatus('QR码格式不正确或不是滚动QR码的一部分');
                }

            } catch (error) {
                console.error('解析图片失败:', error);
                decoder.updateStatus('无法从图片中识别QR码');
                alert('无法从图片中识别QR码，请确保图片清晰且包含有效的QR码');
            }

            // 清空文件输入
            event.target.value = '';
        }

        window.onload = function() {
            const fileInput = document.getElementById('fileInput');
            fileInput.addEventListener('change', handleFileSelect);
            
            // 块选择模式切换
            const blockSelectionMode = document.getElementById('blockSelectionMode');
            const blockSelectionControls = document.getElementById('blockSelectionControls');
            
            blockSelectionMode.addEventListener('change', function() {
                if (this.checked) {
                    blockSelectionControls.style.display = 'block';
                } else {
                    blockSelectionControls.style.display = 'none';
                }
                
                // 如果有正在运行的QR码生成，重新启动以应用新的模式
                const textInput = document.getElementById('textInput').value;
                if (textInput && currentDisplayTimer) {
                    console.log('块选择模式切换，重新生成QR码');
                    generateQR();
                }
            });
        };
    </script>
</body>
</html>

